# AirSense - Smart Indoor Air Quality Monitoring System

## 🌟 Overview

AirSense is a comprehensive indoor air quality monitoring system that provides real-time tracking, analysis, and comparison of air quality metrics across different locations. This repository contains both the original HTML/CSS/JS implementation and a modern React conversion.

## 📁 Project Structure

```
Air-Sense/
├── airsense-react/          # Modern React application (Vite + TypeScript)
├── resources/               # Original HTML/CSS/JS files and assets
├── README.md               # This file
└── .gitignore             # Git ignore rules
```

## 🚀 React Application (airsense-react/)

### Features
- **Real-time Air Quality Monitoring**: Track 9 different air quality metrics + calculated AQI
- **Location-based Tracking**: Monitor multiple indoor locations
- **Comparison Tool**: Side-by-side comparison of air quality between locations
- **Historical Data Visualization**: Placeholder for chart implementations
- **Responsive Design**: Mobile-friendly interface
- **TypeScript**: Full type safety throughout the application

### Tech Stack
- **Frontend**: React 18 + TypeScript
- **Build Tool**: Vite
- **Styling**: CSS3 + Bootstrap 5
- **Routing**: React Router DOM
- **State Management**: React Hooks

### Quick Start

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd Air-Sense
   ```

2. **Navigate to React app**:
   ```bash
   cd airsense-react
   ```

3. **Install dependencies**:
   ```bash
   npm install
   ```

4. **Start development server**:
   ```bash
   npm run dev
   ```

5. **Open in browser**:
   ```
   http://localhost:5173
   ```

### Available Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm run preview` - Preview production build locally
- `npm run lint` - Run ESLint for code quality

### Air Quality Metrics

The system monitors the following metrics:

| Metric | Unit | Good Range | Moderate Range | Poor Range |
|--------|------|------------|----------------|------------|
| Temperature | °C | 21.8-26.1 | 18-21.8, 26.1-30 | <18, >30 |
| Humidity | % | 30-60 | 20-30, 60-70 | <20, >70 |
| PM2.5 | µg/m³ | 0-100 | 100-125 | >125 |
| PM10 | µg/m³ | 0-200 | 200-250 | >250 |
| CO2 Level | ppm | 400-800 | 800-1200 | >1200 |
| Pressure | hPa | 980-1020 | 960-980 | <960 |
| Gas Resistance | Ω | 500-1500 | 1500-2500 | >2500 |
| Nitrogen Dioxide | ppb | 0-110 | 110-130 | >130 |
| Ozone | ppb | 0-100 | 100-120 | >120 |
| **AQI** | - | 0-66 | 67-99 | >99 |

## 📚 Resources Folder

The `resources/` folder contains the original implementation:

### Original Files
- **HTML Pages**: `home1.html`, `home2.html`, `dashboard.html`, `comparison.html`
- **Stylesheets**: `home.css`, `dashboard.css`, `comparison.css`
- **JavaScript**: `dashboard.js`, `comparison.js`, `test.js`
- **Assets**: All original images and icons

### Purpose
- Reference for the original design and functionality
- Backup of the initial implementation
- Resource for understanding the conversion process

## 🔄 Conversion Details

The React application is a **pixel-perfect, functionally-identical** conversion of the original HTML/CSS/JS files with the following improvements:

### Architecture Improvements
- **Component-based**: Reusable React components
- **Type Safety**: Full TypeScript implementation
- **Modern Tooling**: Vite for fast development and building
- **State Management**: React hooks for data flow
- **Routing**: Client-side navigation with React Router

### Preserved Features
- ✅ Exact visual design and layout
- ✅ All original functionality
- ✅ Location selection and persistence
- ✅ Real-time data simulation
- ✅ Air quality calculations
- ✅ Responsive design
- ✅ Bootstrap carousel and components

## 🛠️ Development

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn package manager

### Installation for New Users

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd Air-Sense/airsense-react
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Start development**:
   ```bash
   npm run dev
   ```

The application will be available at `http://localhost:5173`

### Production Deployment

1. **Build the application**:
   ```bash
   npm run build
   ```

2. **Preview the build**:
   ```bash
   npm run preview
   ```

The production files will be in the `dist/` folder.

## 🌐 Browser Support

- ✅ Chrome (latest)
- ✅ Firefox (latest)
- ✅ Safari (latest)
- ✅ Edge (latest)
- ✅ Mobile browsers

## 📝 License

This project is part of an academic/research initiative for indoor air quality monitoring.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📞 Support

For questions or support, please refer to the documentation in the `airsense-react/` folder or check the original implementation in the `resources/` folder.
Air Quality Monitoring System
