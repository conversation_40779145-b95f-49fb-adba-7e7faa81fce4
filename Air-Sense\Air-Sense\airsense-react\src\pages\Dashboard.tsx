import React, { useState, useEffect } from 'react';
import Layout from '../layouts/Layout';
import CircularGauge from '../components/CircularGauge';
import LineChart from '../components/LineChart';
import '../styles/Dashboard.css';

// Air quality standards
const baseAirQualityStandards = {
  Temperature: { 
    poor: { min: -Infinity, max: 18, unit: '°C' },
    moderate: { min: 18, max: 21.8, unit: '°C' },
    good: { min: 21.8, max: 26.10, unit: '°C' },
    moderate2: { min: 26.1, max: 30, unit: '°C' },
    poor2: { min: 30, max: Infinity, unit: '°C' }
  },
  Humidity: {
    poor: { min: -Infinity, max: 20, unit: '%' },
    moderate: { min: 20, max: 30, unit: '%' },
    good: { min: 30, max: 60, unit: '%' },
    moderate2: { min: 60, max: 70, unit: '%' },
    poor2: { min: 70, max: Infinity, unit: '%' }
  },
  'CO2 Level': {
    good: { min: 400, max: 800, unit: 'ppm' },
    moderate: { min: 800, max: 1200, unit: 'ppm' },
    poor: { min: 1200, max: Infinity, unit: 'ppm' }
  },
  'PM2.5': {
    good: { min: 0, max: 100, unit: 'µg/m³' },
    moderate: { min: 100, max: 125, unit: 'µg/m³' },
    poor: { min: 125, max: Infinity, unit: 'µg/m³' }
  },
  'PM10': {
    good: { min: 0, max: 200, unit: 'µg/m³' },
    moderate: { min: 200, max: 250, unit: 'µg/m³' },
    poor: { min: 250, max: Infinity, unit: 'µg/m³' }
  },
  'Gas Resistance': {
    good: { min: 50, max: Infinity, unit: 'kΩ' },
    moderate: { min: 10, max: 50, unit: 'kΩ' },
    poor: { min: 0, max: 10, unit:'kΩ' }
  },
  'Pressure': {
    good: { min: 980, max: 1020, unit: 'hPa' },
    moderate: { min: 960, max: 980, unit: 'hPa' },
    poor: { min: -Infinity, max: 960, unit: 'hPa' }
  },
  'Nitrogen Dioxide (NO2)': {
    good: { min: 0, max: 110, unit: 'ppb' },
    moderate: { min: 110, max: 130, unit: 'ppb' },
    poor: { min: 130, max: Infinity, unit: 'ppb' }
  },
  'Ozone (O3)': {
    good: { min: 0, max: 100, unit: 'ppb' },
    moderate: { min: 100, max: 120, unit: 'ppb' },
    poor: { min: 120, max: Infinity, unit: 'ppb' }
  }
};

// Calculate AQI based on multiple metrics
const calculateAQI = (metrics: Record<string, number>) => {
  const breakpoints = {
    pm25: [
      { low: 0, high: 12, aqiLow: 0, aqiHigh: 50 },
      { low: 12.1, high: 35.4, aqiLow: 51, aqiHigh: 100 },
      { low: 35.5, high: 55.4, aqiLow: 101, aqiHigh: 150 },
      { low: 55.5, high: 150.4, aqiLow: 151, aqiHigh: 200 },
      { low: 150.5, high: 250.4, aqiLow: 201, aqiHigh: 300 },
      { low: 250.5, high: 500.4, aqiLow: 301, aqiHigh: 500 }
    ]
  };

  const pm25Value = metrics['PM2.5'] || 0;
  let aqi = 0;

  for (const bp of breakpoints.pm25) {
    if (pm25Value >= bp.low && pm25Value <= bp.high) {
      aqi = Math.round(((bp.aqiHigh - bp.aqiLow) / (bp.high - bp.low)) * (pm25Value - bp.low) + bp.aqiLow);
      break;
    }
  }

  return Math.min(500, Math.max(0, aqi));
};

// Mock data generator with more realistic values
const generateMockData = () => {
  const data = {
    Temperature: Math.round((Math.random() * 8 + 20) * 10) / 10, // 20-28°C
    Humidity: Math.round(Math.random() * 30 + 40), // 40-70%
    'PM2.5': Math.round(Math.random() * 100 + 20), // 20-120 µg/m³
    'PM10': Math.round(Math.random() * 150 + 50), // 50-200 µg/m³
    'Nitrogen Dioxide (NO2)': Math.round(Math.random() * 100 + 20), // 20-120 ppb
    'Ozone (O3)': Math.round(Math.random() * 80 + 40), // 40-120 ppb
    'CO2 Level': Math.round(Math.random() * 600 + 400), // 400-1000 ppm
    'Pressure': Math.round(Math.random() * 40 + 980), // 980-1020 hPa
    'Gas Resistance': Math.round(Math.random() * 1000 + 500) // 500-1500 Ω
  };

  // Calculate AQI
  const aqi = calculateAQI(data);
  return { ...data, AQI: aqi };
};

const getQualityStatus = (metric: string, value: number) => {
  if (metric === 'AQI') {
    if (value <= 66) return 'good';
    if (value <= 99) return 'moderate';
    return 'poor';
  }

  const standards = baseAirQualityStandards[metric as keyof typeof baseAirQualityStandards];
  if (!standards) return 'moderate';

  // Handle Temperature and Humidity with multiple ranges
  if (metric === 'Temperature' || metric === 'Humidity') {
    if (value >= standards.good.min && value <= standards.good.max) return 'good';
    if ((value >= standards.moderate.min && value <= standards.moderate.max) ||
        (standards.moderate2 && value >= standards.moderate2.min && value <= standards.moderate2.max)) {
      return 'moderate';
    }
    return 'poor';
  }

  // Handle other metrics
  for (const [status, range] of Object.entries(standards)) {
    if (status.includes('2')) continue; // Skip duplicate ranges
    if (value >= range.min && value <= range.max) {
      return status;
    }
  }
  return 'moderate';
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'good': return 'success';
    case 'moderate': return 'warning';
    case 'poor': return 'danger';
    default: return 'secondary';
  }
};

// Generate chart data for historical trends
const generateChartData = (metric: string, dateFrom: string, dateTo: string) => {
  const startDate = new Date(dateFrom);
  const endDate = new Date(dateTo);
  const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;

  const labels = Array.from({ length: days }, (_, i) => {
    const date = new Date(startDate);
    date.setDate(startDate.getDate() + i);
    return date.toLocaleDateString();
  });

  // Generate realistic data based on metric type
  const getBaseValue = (metric: string) => {
    const baseValues: { [key: string]: number } = {
      'AQI': 75,
      'Temperature': 23,
      'Humidity': 45,
      'PM2.5': 35,
      'PM10': 50,
      'CO2 Level': 600,
      'Gas Resistance': 800,
      'Pressure': 1013,
      'Nitrogen Dioxide (NO2)': 25,
      'Ozone (O3)': 30
    };
    return baseValues[metric] || 50;
  };

  const baseValue = getBaseValue(metric);
  const data = labels.map(() => {
    const variation = (Math.random() - 0.5) * (baseValue * 0.3);
    return Math.max(0, baseValue + variation);
  });

  // Get color for this metric
  const getMetricColor = (metric: string) => {
    const colorMap: { [key: string]: string } = {
      'AQI': '#EA4335',
      'Temperature': '#34A853',
      'Humidity': '#FBBC05',
      'PM2.5': '#4285F4',
      'PM10': '#9C27B0',
      'CO2 Level': '#FF9800',
      'Gas Resistance': '#795548',
      'Pressure': '#607D8B',
      'Nitrogen Dioxide (NO2)': '#E91E63',
      'Ozone (O3)': '#00BCD4'
    };
    return colorMap[metric] || '#4FB8A3';
  };

  const color = getMetricColor(metric);

  return {
    labels,
    datasets: [
      {
        label: metric,
        data,
        borderColor: color,
        backgroundColor: color + '20', // Add transparency
        fill: true,
        tension: 0.3,
      },
    ],
  };
};

const Dashboard: React.FC = () => {
  const [selectedLocation, setSelectedLocation] = useState<string>('');
  const [selectedMetric, setSelectedMetric] = useState<string>('Temperature');
  const [dateFrom, setDateFrom] = useState<string>('');
  const [dateTo, setDateTo] = useState<string>('');
  const [currentData, setCurrentData] = useState<Record<string, number>>({});
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [showChart, setShowChart] = useState<boolean>(false);
  const [chartData, setChartData] = useState<any>(null);

  useEffect(() => {
    // Load location from localStorage
    const storedLocation = localStorage.getItem('userLocation');
    if (storedLocation) {
      setSelectedLocation(storedLocation);
    }

    // Generate initial mock data
    setCurrentData(generateMockData());

    // Set default dates
    const today = new Date();
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    setDateFrom(weekAgo.toISOString().split('T')[0]);
    setDateTo(today.toISOString().split('T')[0]);

    // Set up real-time updates every 30 seconds
    const interval = setInterval(() => {
      setCurrentData(generateMockData());
      setLastUpdate(new Date());
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const metrics = ['AQI', ...Object.keys(baseAirQualityStandards)];

  const getUnit = (metric: string) => {
    if (metric === 'AQI') return '';
    const standards = baseAirQualityStandards[metric as keyof typeof baseAirQualityStandards];
    return standards?.good?.unit || '';
  };

  // Get unique color for each metric
  const getMetricColor = (metric: string) => {
    const colorMap: { [key: string]: string } = {
      'AQI': '#EA4335',
      'Temperature': '#34A853',
      'Humidity': '#FBBC05',
      'PM2.5': '#4285F4',
      'PM10': '#9C27B0',
      'CO2 Level': '#FF9800',
      'Gas Resistance': '#795548',
      'Pressure': '#607D8B',
      'Nitrogen Dioxide (NO2)': '#E91E63',
      'Ozone (O3)': '#00BCD4'
    };
    return colorMap[metric] || '#4FB8A3';
  };

  const handleUpdateChart = () => {
    if (!dateFrom || !dateTo) {
      alert('Please select both start and end dates');
      return;
    }

    const newChartData = generateChartData(selectedMetric, dateFrom, dateTo);
    // Update chart data with metric-specific color
    if (newChartData.datasets[0]) {
      const color = getMetricColor(selectedMetric);
      newChartData.datasets[0].borderColor = color;
      newChartData.datasets[0].backgroundColor = color + '20'; // Add transparency
    }
    setChartData(newChartData);
    setShowChart(true);
  };

  return (
    <Layout>
      <header className="welcome-section">
        <h1>Welcome to the Smart Indoor Air Quality Monitoring System</h1>
        <p className="subtitle">Track, analyze, and improve indoor air quality with ease</p>
      </header>

      <main className="dashboard-container">
        {/* Air Quality Scale */}
        <div className="quality-scale-card">
          <h2>Air Quality Scale</h2>
          <div className="scale-bar"></div>
          <div className="scale-labels">
            <span>Good</span>
            <span>Moderate</span>
            <span>Poor</span>
          </div>
        </div>

        {/* Current Location Display */}
        {selectedLocation && (
          <div className="alert alert-info mb-4 d-flex justify-content-between align-items-center">
            <div>
              <strong>Current Location:</strong> {selectedLocation.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
            </div>
            <small className="text-muted">
              Last updated: {lastUpdate.toLocaleTimeString()}
            </small>
          </div>
        )}

        {/* Metrics Grid */}
        <div className="metrics-grid">
          {metrics.map((metric, index) => {
            const value = currentData[metric] || 0;
            const status = getQualityStatus(metric, value);
            const unit = getUnit(metric);

            // Get max value for gauge based on metric
            const getMaxValue = (metric: string) => {
              const maxValues: { [key: string]: number } = {
                'AQI': 500,
                'Temperature': 40,
                'Humidity': 100,
                'PM2.5': 300,
                'PM10': 500,
                'CO2 Level': 2000,
                'Gas Resistance': 2000,
                'Pressure': 1050,
                'Nitrogen Dioxide (NO2)': 200,
                'Ozone (O3)': 200
              };
              return maxValues[metric] || 100;
            };

            // Get unique color for each metric
            const getMetricColor = (metric: string, index: number) => {
              const colors = [
                '#EA4335', // AQI - Red
                '#34A853', // Temperature - Green
                '#FBBC05', // Humidity - Yellow
                '#4285F4', // PM2.5 - Blue
                '#9C27B0', // PM10 - Purple
                '#FF9800', // CO2 Level - Orange
                '#795548', // Gas Resistance - Brown
                '#607D8B', // Pressure - Blue Grey
                '#E91E63', // Nitrogen Dioxide - Pink
                '#00BCD4'  // Ozone - Cyan
              ];
              return colors[index % colors.length];
            };

            return (
              <CircularGauge
                key={metric}
                value={value}
                maxValue={getMaxValue(metric)}
                unit={unit}
                title={metric}
                status={status as 'good' | 'moderate' | 'poor'}
                size={180}
              />
            );
          })}
        </div>

        {/* Real-time Controls */}
        <div className="card mb-4">
          <div className="card-body">
            <div className="d-flex justify-content-between align-items-center">
              <h5 className="card-title mb-0">Real-time Monitoring</h5>
              <button
                className="btn btn-outline-primary"
                onClick={() => {
                  setCurrentData(generateMockData());
                  setLastUpdate(new Date());
                }}
              >
                <i className="bi bi-arrow-clockwise me-2"></i>
                Refresh Data
              </button>
            </div>
            <p className="text-muted mt-2 mb-0">
              Data updates automatically every 30 seconds. Current location: {selectedLocation || 'Not selected'}
            </p>
          </div>
        </div>

        {/* Historical Data Section */}
        <div className="trends-section card">
          <div className="card-body">
            <div className="trends-header d-flex justify-content-between align-items-center mb-3 flex-wrap">
              <h2 className="mb-2 mb-md-0">Historical Data Analysis</h2>
              <div className="trends-controls d-flex gap-2 flex-wrap">
                <select
                  value={selectedMetric}
                  onChange={(e) => setSelectedMetric(e.target.value)}
                  className="form-select"
                  style={{ minWidth: '150px' }}
                >
                  {metrics.map(metric => (
                    <option key={metric} value={metric}>{metric}</option>
                  ))}
                </select>
                <input
                  type="date"
                  value={dateFrom}
                  onChange={(e) => setDateFrom(e.target.value)}
                  className="form-control"
                  style={{ width: 'auto' }}
                />
                <input
                  type="date"
                  value={dateTo}
                  onChange={(e) => setDateTo(e.target.value)}
                  className="form-control"
                  style={{ width: 'auto' }}
                />
                <button className="btn btn-primary" onClick={handleUpdateChart}>
                  <i className="bi bi-graph-up me-2"></i>
                  Update Chart
                </button>
              </div>
            </div>

            {showChart && chartData ? (
              <div className="chart-container bg-light p-4 rounded border" style={{ height: '400px' }}>
                <LineChart
                  data={chartData}
                  height={350}
                />
              </div>
            ) : (
              <div className="chart-placeholder bg-light p-4 rounded border text-center" style={{ height: '400px' }}>
                <div className="d-flex align-items-center justify-content-center h-100">
                  <div>
                    <i className="bi bi-graph-up" style={{ fontSize: '3rem', color: '#6c757d' }}></i>
                    <p className="mt-3 text-muted">Select date range and click "Update Chart" to view historical data</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
    </Layout>
  );
};

export default Dashboard;
