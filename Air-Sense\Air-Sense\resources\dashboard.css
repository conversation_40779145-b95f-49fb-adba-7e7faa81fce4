:root {
    --primary-color: #4FB8A3;
    --secondary-color: #3A8A98;
    --accent-color: #2E5C6E;
    --background-color: #f8f9fe;
    --card-background: #FFFFFF;
    --text-primary: #2C3E50;
    --text-secondary: #5F6368;
    --success-color: #34A853;
    --warning-color: #FBBC05;
    --danger-color:#EA4335; 
    --border-radius: 12px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0; 
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
   
}

html,body {
    background-color: var(--background-color);
    color: var(--text-primary);
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    overflow-x: hidden; /* Prevent horizontal scrolling */
}

   

.quality-scale-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
      
    max-width: 1400px;
    margin: 2rem auto;
}

.quality-scale-card h2 {
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    color: var(--text-primary);
}

.scale-bar {
    height: 8px;
    background: linear-gradient(to right, 
        #34A853,  /* Good - Green */
        #FBBC05,  /* Moderate - Yellow */
        #EA4335   /* Poor - Red */
        
    );
    border-radius: 4px;
    margin: 1rem 0;
    
}

.scale-labels {
    display: flex;
    justify-content: space-between;
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.9rem;
}

.nav-bar {
  background: white;
  padding: 15px 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  color:rgb(81, 72, 72);
  min-height: 80px;
  position: relative;
}
.nav-links {
  flex: 1;
  display: flex;
  justify-content: center;
  gap: 2rem;
}
.search-container {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 1rem;
}
.home-icon-btn {
  background: none;
  border: none;
  outline: none;
  cursor: pointer;
  padding: 0.4rem;
}
.home-icon{
  height: 32px;
  width: 32px;
  border: none;
  outline: none;
}
.location-dropdown {
  color: black;
  text-transform: capitalize;
  font-weight: 500;
}
.logo-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.logo-wrapper {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo {
  height: 80px;
  border-radius: 10px
}
.comparison-location-bar {
  flex: 1;
  display: flex;
  justify-content: flex-end; /* ✅ Aligns dropdown & home icon to right */
  align-items: center;
  gap: 1rem;
}

.nav-links a {
  text-decoration: none;
  color: var(--text-secondary);
  font-weight: 500;
  padding: 0.75rem 1.8rem;
  border-radius: 6px;
  transition: all 0.3s ease;
  font-size: 1.4rem;
}

.nav-links a.active,
.nav-links a:hover {
  color: var(--primary-color);
  background: rgba(79, 184, 163, 0.1);
}


 .chart-container {
  position: relative;
  height: 400px; /* REQUIRED to render the chart */
  width: 100%;
}

.welcome-section {
    text-align: center;
    padding: 3rem 2rem;
    background: linear-gradient(135deg, rgba(79, 184, 163, 0.1), rgba(46, 92, 110, 0.1));
}

.welcome-section h1 {
    font-size: 2.5rem;
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.subtitle {
    color: var(--text-secondary);
    font-size: 1.3rem;
}

.container {
    
    width: 100vw;
    max-width: 100%;
    padding: 10px;
    box-sizing: border-box;

}

.aqi-section {
    display: flex;
    justify-content: center;
    margin: 2.5rem 0;
    padding-top: 1.5rem;
}

.aqi-card {
    background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.95));
    border: 2px solid rgba(79, 184, 163, 0.3);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    max-width: 400px;
    margin: 0 auto 2rem;
}

.aqi-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 20px rgba(0, 0, 0, 0.15);
}

.aqi-card h3 {
    font-size: 1.5rem;
    color: var(--accent-color);
    margin-bottom: 1.5rem;
    text-align: center;
}

.aqi-gauge {
    width: 220px;
    height: 220px;
    margin: 1rem auto;
}


.aqi-gauge .gauge-container {
    width: 250px;
    height: 250px;
    margin: 1.5rem auto;
}

.aqi-gauge .gauge-center {
    width: 75%;
    height: 75%;
}

.aqi-gauge .value {
    font-size: 3rem;
    font-weight: bold;
    color: var(--primary-color);
}

.aqi-gauge .unit {
    font-size: 1.2rem;
    color: var(--text-secondary);
}

.aqi-gauge .gauge-fill {
    background: conic-gradient(
        #4FB8A3 0%, /* Start with primary color */
        #3A8A98 33%, /* Transition to secondary color */
        #2E5C6E 66%, /* Transition to accent color */
        #EA4335 100% /* End with a warning color */
    );
}
.metrics-grid {
  
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    max-width: 1400px;
    margin: 2rem auto;
 
}

.metric-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    text-align: center;
    transition: transform 0.2s ease;
    width:280px;
}
.metric-card.aqi-card {
    background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.95));
    border: 2px solid rgba(79, 184, 163, 0.3);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.metric-card.aqi-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 20px rgba(0, 0, 0, 0.15);
}

/* Remove the old aqi-section styles since we're not using them anymore */
.aqi-section {
    display: none;
}
.metric-card:hover {
    transform: translateY(-3px);
}

.metric-card h3 {
    color: var(--accent-color);
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.gauge-container {
    position: relative;
    width: 180px;
    height: 180px;
    margin: 1rem auto;
}
.value-display {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 1;
}
.value-display .value {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--text-primary);
    line-height: 1;
}

.value-display .unit {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-top: 0.2rem;
}

.gauge {
    width: 100%;
    height: 100%;
    position: relative;
    border-radius: 50%;
    background: #f0f0f0;
    overflow: hidden;
}

.gauge-fill {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: conic-gradient(
        var(--primary-color) var(--percentage, 70%),
        transparent var(--percentage, 70%)
    );
    transform-origin: 50% 50%;
}

.gauge-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 50%;
    width: 80%;
    height: 80%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.value {
    font-size: 2rem;
    font-weight: bold;
    color: var(--text-primary);
}

.unit {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.status {
    display: inline-block;
    padding: 0.4rem 1.2rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    margin-top: 1rem;
}

.status.good {
    background: rgba(52, 168, 83, 0.1);
    color: var(--success-color);
}

.status.moderate {
    background: rgba(251, 188, 5, 0.1);
    color: var(--warning-color);
}

.status.poor {
    background: rgba(234, 67, 53, 0.1);
    color: var(--danger-color);
}
.trends-section {
    background: white;
    border-radius: var(--border-radius);
    padding: 1rem;
    box-shadow: var(--box-shadow);
    max-width: 1400px;
    margin: 2rem auto;
    padding: 2rem;
}
.trends-section h2 {
    font-size: 1.8rem;  /* Increased heading size */
    color: var(--text-primary);
    margin-bottom: 1.5rem;
}

.trends-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.trends-controls {
    display: flex;
    gap: 1rem;
    font-size: 1.2rem;
    padding: 0.75rem 1.2rem;
   
}

.control-input {
    padding: 0.75rem 1.2rem;
    border: 1px solid #E1E3E6;
    border-radius: 6px;
    background: white;
    color: var(--text-primary);
    font-size: 1.2rem;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.chart-container {
    
    height: 400px;
    position: relative;
    margin-top: 2rem;
}

.dashboard-wrapper {
    display: flex;
    flex-wrap: wrap;  /* Allows items to wrap if necessary */
    justify-content: center;
    align-items: center;
}

.dashboard-content {
    width: 100%;
    max-width: 1200px; /* Adjust based on layout */
    padding: 20px;
}
@media (max-width: 768px) {
    

    .nav-links {
        display: none;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .trends-header {
        flex-direction: column;
        gap: 1rem;
    }
    
    .trends-controls {
        flex-wrap: wrap;
    }
    .dashboard-wrapper {
        flex-direction: column; /* Stack elements */
        align-items: center;
    }

    .dashboard-section {
        width: 100%;
        padding: 10px;
    }
}
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
    width: 100%;
}

.metric-card {
    background: #ffffff;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    text-align: center;
    min-width: 220px;
}

/* Add responsive adjustments */
@media (min-width: 1200px) {
    .metrics-grid {
        grid-template-columns: repeat(5, 1fr);
    }
    .metric-card.aqi-card {
        grid-column: span 1;
    }
}

@media (max-width: 1199px) and (min-width: 992px) {
    .metrics-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 991px) and (min-width: 768px) {
    .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 767px) {
    .metrics-grid {
        grid-template-columns: 1fr;
    }
}
.footer {
    background-color: #2c3e50;
    color: white;
    padding: 1.5rem;
    text-align: center;
  }
  .footer-links {
    margin: 1rem 0;
  }
  .footer-links a {
    color: #bbbebd;
    text-decoration: none;
    margin: 0 1rem;
    font-weight: bold;
  }
  .footer-links a:hover {
    text-decoration: underline;
  }
  .footer-social-icons {
    margin-top: 1rem;
  }
  .footer-social-icons img {
    height: 50px;
    width: 50px;
    margin: 0 0.5rem;
    vertical-align: middle;
    border-radius: 10px;
    gap: 300px;
  }