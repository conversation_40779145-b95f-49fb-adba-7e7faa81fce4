# AirSense - Smart Indoor Air Quality Monitoring System

A React application built with Vite and TypeScript for monitoring indoor air quality. This project converts the original HTML/CSS/JS files into a modern React structure while preserving the exact UI/UX design and content.

## Features

- **Real-Time Monitoring**: Live air quality readings tailored to your environment
- **Historical Data Access**: Analyze past data to identify patterns and ensure long-term safety
- **Air Quality Measurement**: Continuous monitoring of CO2, VOCs, particulate matter, and more
- **Location Comparison**: Compare air quality metrics between different locations
- **Responsive Design**: Works seamlessly on desktop and mobile devices

## Tech Stack

- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **Styling**: Bootstrap 5 + Custom CSS
- **Routing**: React Router DOM
- **State Management**: React Hooks

## Project Structure

```
your-react-app/
├── public/                     # Static files
│   └── index.html             # Main HTML template
├── src/
│   ├── assets/                # Images, icons, etc.
│   │   ├── logo.jpg
│   │   ├── background.jpg
│   │   ├── live_air.jpg
│   │   ├── history.jpg
│   │   ├── air_index.jpg
│   │   ├── comparison.png
│   │   ├── home.png
│   │   ├── fb.avif
│   │   ├── twitter.webp
│   │   └── linkedin.webp
│   ├── components/            # Reusable UI components
│   │   ├── Navbar.tsx         # Navigation header
│   │   └── Footer.tsx         # Footer component
│   ├── pages/                 # Route-level components (screens)
│   │   ├── Home.tsx           # Home page
│   │   ├── Dashboard.tsx      # Dashboard page
│   │   └── Comparison.tsx     # Comparison page
│   ├── layouts/               # Common layout wrappers
│   │   └── Layout.tsx         # Main layout component
│   ├── App.tsx                # Main component
│   ├── main.tsx               # Entry point (for Vite)
│   └── index.css              # Global styles
├── package.json
├── vite.config.ts             # Vite configuration
├── tsconfig.json              # TypeScript configuration
└── README.md
```

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn

### Installation

1. Navigate to the project directory:
   ```bash
   cd your-react-app
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Run the development server:
   ```bash
   npm run dev
   ```

4. Open [http://localhost:5173](http://localhost:5173) in your browser

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## Pages

### Home Page (`/`)
- Welcome section with hero background
- Features carousel showcasing system capabilities
- Location selector (preserved from original design)
- Exact replica of original home1.html and home2.html

### Dashboard (`/dashboard`)
- Air quality metrics display with color-coded status
- Historical data visualization section
- Real-time monitoring interface
- Exact replica of original dashboard.html

### Comparison (`/comparison`)
- Side-by-side location comparison
- Metric selection and date filtering
- Visual comparison with status indicators
- Exact replica of original comparison.html

## Components

### Navbar
- Navigation menu with active state highlighting
- Location selector with localStorage persistence
- Home icon for location reset
- Responsive Bootstrap design

### Footer
- Copyright information
- Social media links (Facebook, Twitter, LinkedIn)
- Additional navigation links
- Consistent across all pages

### Layout
- Common wrapper for all pages
- Includes Navbar and Footer
- Flexible content area

## Styling

The application preserves the original styling with:
- Bootstrap 5 for responsive grid and components
- Custom CSS for specific air quality UI elements
- Original background images and color schemes
- Exact font sizes, spacing, and visual hierarchy

## Data

Currently uses mock data for demonstration purposes. The original JavaScript functionality has been converted to React hooks:
- Location selection with localStorage persistence
- Mock air quality data generation
- Status calculation based on air quality thresholds
- Form validation and user interactions

## Key Features Preserved

- ✅ **Exact UI/UX Design**: All visual elements match the original HTML/CSS
- ✅ **Location Selection**: Dropdown with localStorage persistence
- ✅ **Navigation**: Active state highlighting and routing
- ✅ **Air Quality Metrics**: Color-coded status indicators
- ✅ **Responsive Carousel**: Bootstrap carousel for features
- ✅ **Form Validation**: Location comparison validation
- ✅ **Mock Data**: Realistic air quality data generation

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Development Notes

This project was created by converting existing HTML/CSS/JS files to React while:
- Maintaining exact visual design and user experience
- Preserving all content and functionality
- Using modern React patterns and TypeScript
- Following the specified project structure
- Keeping all original images and styling

## Future Enhancements

- Integration with real air quality sensors/APIs
- Chart.js implementation for data visualization
- Database integration for historical data
- User authentication and personalization
- Real-time WebSocket connections

## License

This project is licensed under the MIT License.
