import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Layout from '../layouts/Layout';

const Home: React.FC = () => {
  const [selectedLocation, setSelectedLocation] = useState<string>('');

  useEffect(() => {
    // Restore selected location from localStorage
    const storedLocation = localStorage.getItem('userLocation');
    if (storedLocation) {
      setSelectedLocation(storedLocation);
    }
  }, []);

  const handleLocationChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const location = event.target.value;
    setSelectedLocation(location);
    localStorage.setItem('userLocation', location);
  };

  const handleHomeClick = () => {
    localStorage.removeItem('userLocation');
    setSelectedLocation('');
  };

  // Check if user has selected a location to determine layout
  const showNavbar = !!selectedLocation;

  return (
    <Layout showNavbar={showNavbar}>
      <section className="hero">
        {!showNavbar && (
          <div className="search-container" style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '1rem',
            margin: '0 auto'
          }}>
            <button
              className="home-icon-btn"
              onClick={handleHomeClick}
              style={{
                background: 'none',
                border: 'none',
                outline: 'none',
                cursor: 'pointer',
                padding: '0.4rem'
              }}
            >
              <img
                src="/home.png"
                alt="Home"
                className="home-icon"
                style={{
                  height: '32px',
                  width: '32px',
                  border: 'none',
                  outline: 'none'
                }}
              />
            </button>

            <select
              className="location-dropdown"
              value={selectedLocation}
              onChange={handleLocationChange}
              title="Please select your new location, when you change your location which is going to measure the air quality"
              style={{
                padding: '0.5rem 1rem',
                borderRadius: '6px',
                border: '1px solid #ccc',
                fontSize: '1rem',
                cursor: 'pointer'
              }}
            >
              <option value="" disabled>Select your location</option>
              <option value="deans-office">Dean's Office</option>
              <option value="basement">Basement</option>
              <option value="lecture-hall">Lecture Hall</option>
            </select>
          </div>
        )}
        {!showNavbar && <><br /><br /></>}
        <h5></h5>
        <h1><b>Welcome to the Smart Indoor Air Quality Monitoring System</b></h1><br />
        <img src="/logo.jpg" style={{ height: '400px', borderRadius: '30px' }} alt="AirSense Logo" />
        <br />
        <p>Track, analyze, and improve indoor air quality with ease</p>
        
        <section className="features">
          <div className="container1">
            <h2 style={{ fontSize: '40px' }}>Our Features</h2>
            <div id="featureCarousel" className="carousel slide" data-bs-ride="carousel">
              {/* Indicators */}
              <div className="carousel-indicators">
                <button type="button" data-bs-target="#featureCarousel" data-bs-slide-to="0" className="active" aria-current="true" aria-label="Feature 1"></button>
                <button type="button" data-bs-target="#featureCarousel" data-bs-slide-to="1" aria-label="Feature 2"></button>
                <button type="button" data-bs-target="#featureCarousel" data-bs-slide-to="2" aria-label="Feature 3"></button>
                <button type="button" data-bs-target="#featureCarousel" data-bs-slide-to="3" aria-label="Feature 4"></button>
              </div>

              {/* Slides */}
              <div className="carousel-inner">
                {/* Feature 1 */}
                <div className="carousel-item active">
                  <div className="feature-item text-center">
                    <h3>Real-Time Monitoring</h3>
                    <img src="/live_air.jpg" alt="Location Selection" style={{ height: '300px' }} />
                    <p>Stay updated with live air quality readings tailored to your environment.</p>
                  </div>
                </div>
                {/* Feature 2 */}
                <div className="carousel-item">
                  <div className="feature-item text-center">
                    <h3>Access to Historical Data</h3>
                    <img src="/history.jpg" alt="Historical Data" style={{ height: '300px' }} />
                    <p>Analyze past data to identify patterns and ensure long-term safety.</p>
                  </div>
                </div>
                {/* Feature 3 */}
                <div className="carousel-item">
                  <div className="feature-item text-center">
                    <h3>Air Quality Measurement</h3>
                    <img src="/air_index.jpg" alt="Air Quality Measurement" style={{ height: '300px' }} />
                    <p>Continuously measures air quality parameters, including CO2, VOCs, particulate matter, and more.</p>
                  </div>
                </div>
                {/* Feature 4 */}
                <div className="carousel-item">
                  <div className="feature-item text-center">
                    <h3>Comparison of Air Quality</h3>
                    <img src="/comparison.png" alt="Air Quality Comparison" style={{ height: '300px' }} />
                    <p>Displays graphical comparisons of air quality metrics between two selected locations effectively.</p>
                  </div>
                </div>
              </div>

              {/* Controls */}
              <button className="carousel-control-prev" type="button" data-bs-target="#featureCarousel" data-bs-slide="prev">
                <span className="carousel-control-prev-icon" aria-hidden="true"></span>
                <span className="visually-hidden">Previous</span>
              </button>
              <button className="carousel-control-next" type="button" data-bs-target="#featureCarousel" data-bs-slide="next">
                <span className="carousel-control-next-icon" aria-hidden="true"></span>
                <span className="visually-hidden">Next</span>
              </button>
            </div>
          </div>
        </section>
        <br /><br />
      </section>
    </Layout>
  );
};

export default Home;
