/* General Styles */
:root {
    --primary-color: #4FB8A3;
    --secondary-color: #3A8A98;
    --accent-color: #2E5C6E;
    --background-color: #f8f9fe;
    --card-background: #FFFFFF;
    --text-primary: #2C3E50;
    --text-secondary: #5F6368;
    --success-color: #34A853;
    --warning-color: #FBBC05;
    --danger-color: #EA4335;
    --border-radius: 12px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
} 




* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
    line-height: 1.6;
    background-color: #f5f5f5;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Navigation Bar */
.nav-bar {
  background: white;
  padding: 15px 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  color:rgb(81, 72, 72);
  min-height: 80px;
  position: relative;
}
.nav-links {
  flex: 1;
  display: flex;
  justify-content: center;
  gap: 2rem;
}
.search-container {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 1rem;
}
.home-icon-btn {
  background: none;
  border: none;
  outline: none;
  cursor: pointer;
  padding: 0.4rem;
}
.home-icon{
  height: 32px;
  width: 32px;
  border: none;
  outline: none;
}
.location-dropdown {
  color: black;
  text-transform: capitalize;
  font-weight: 500;
}
.logo-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.logo-wrapper {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo {
  height: 80px;
  border-radius: 10px
}
.comparison-location-bar {
  flex: 1;
  display: flex;
  justify-content: flex-end; /* ✅ Aligns dropdown & home icon to right */
  align-items: center;
  gap: 1rem;
}

.nav-links a {
  text-decoration: none;
  color: var(--text-secondary);
  font-weight: 500;
  padding: 0.75rem 1.8rem;
  border-radius: 6px;
  transition: all 0.3s ease;
  font-size: 1.4rem;
}

.nav-links a.active,
.nav-links a:hover {
  color: var(--primary-color);
  background: rgba(79, 184, 163, 0.1);
}


.welcome-section {
    text-align: center;
    padding: 3rem 2rem;
    background: linear-gradient(135deg, rgba(79, 184, 163, 0.1), rgba(46, 92, 110, 0.1));
}

.welcome-section h1 {
    font-size: 2.5rem;
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.subtitle {
    color: var(--text-secondary);
    font-size: 1.3rem;
}

/* Main Container */
.container {
    max-width: 100%;
    margin: 0 auto;
    padding: 2rem;
    flex: 1;
    margin-bottom: 4rem;
}

.container h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: #333;
}

/* Legend Styles */
.legend {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
}

.dot.good {
    background-color: #28a745;
}

.dot.moderate {
    background-color: #ffc107;
}

.dot.poor {
    background-color: #dc3545;
}

/* Status Colors */
.status.good {
    color: #28a745;
}

.status.moderate {
    color: #ffc107;
}

.status.poor {
    color: #dc3545;
}

/* Validation and Error Messages */
.validation-message, .chart-error {
    background-color: #f8d7da;
    color: #721c24;
    padding: 1rem;
    margin: 1rem 0;
    border-radius: 4px;
    text-align: center;
    display: none;
}

/* Comparison Grid */
.comparison-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin-bottom: 2rem;
}

/* Location Cards */
.location-card {
    background-color: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.location-card h3 {
    margin-bottom: 1.5rem;
    color: #333;
}

/* Form Controls */
.location-select,
.metrics-select select,
input[type="date"] {
    width: 100%;
    padding: 0.5rem;
    margin-bottom: 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Gauge Container */
.gauge-container {
    position: relative;
    width: 200px;
    height: 200px;
    margin: 2rem auto;
}

.value-display {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 1.5rem;
    font-weight: bold;
}

/* Status Indicators */
.status {
    text-align: center;
    padding: 0.5rem;
    border-radius: 4px;
    margin-top: 1rem;
    font-weight: bold;
}

/* Comparison Summary and Daily Distribution */
.comparison-summary, .daily-distribution {
    background-color: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-top: 2rem;
}

.chart-container {
    /*
    position: relative;
    height: 300px;
    margin-top: 1rem;
    */
    width: 90%; /* Ensure it takes full width */
    max-width: 1000px; /* Adjust as needed */
    margin: 20px auto; /* Centering */
    padding: 20px;
    margin-bottom: 50px;
}

.comparison-text {
    margin-top: 1rem;
    line-height: 1.8;
}

/* Footer */
.footer {
    background-color:#333;
    color: white;
    padding: 2rem;
    margin-top: auto;
    text-align: center;
}

.footer-links {
    margin: 1rem 0;
}

.footer-links a {
    color: white;
    text-decoration: none;
    margin: 0 1rem;
}

.footer-social-icons img {
    width: 30px;
    height: 30px;
    margin: 0 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .comparison-grid {
        grid-template-columns: 1fr;
    }
    
    .nav-bar {
        flex-direction: column;
        gap: 1rem;
    }
    
    .user-info-wrapper {
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
    }

    .container {
        padding: 1rem;
        margin-bottom: 2rem;
    }

    .legend {
        flex-direction: column;
        align-items: center;
    }
}

.footer {
    background-color: #2c3e50;
    color: white;
    padding: 1.5rem;
    text-align: center;
  }
  .footer-links {
    margin: 1rem 0;
  }
  .footer-links a {
    color: #bbbebd;
    text-decoration: none;
    margin: 0 1rem;
    font-weight: bold;
  }
  .footer-links a:hover {
    text-decoration: underline;
  }
  .footer-social-icons {
    margin-top: 1rem;
  }
  .footer-social-icons img {
    height: 50px;
    width: 50px;
    margin: 0 0.5rem;
    vertical-align: middle;
    border-radius: 10px;
    gap: 300px;
  }