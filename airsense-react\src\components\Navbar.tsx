import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';

const Navbar: React.FC = () => {
  const [selectedLocation, setSelectedLocation] = useState<string>('');
  const location = useLocation();

  useEffect(() => {
    // Restore selected location from localStorage
    const storedLocation = localStorage.getItem('userLocation');
    if (storedLocation) {
      setSelectedLocation(storedLocation);
    }
  }, []);

  const handleLocationChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const location = event.target.value;
    setSelectedLocation(location);
    localStorage.setItem('userLocation', location);
  };

  const handleHomeClick = () => {
    localStorage.removeItem('userLocation');
    setSelectedLocation('');
  };

  const isActive = (path: string) => location.pathname === path;

  return (
    <nav className="nav-bar">
      <div className="logo-container">
        <div className="logo-wrapper">
          <img src="/logo.jpg" alt="AirGuard Logo" className="logo" />
        </div>
      </div>

      <div className="nav-links">
        <Link
          className={isActive('/') ? 'active' : ''}
          to="/"
        >
          Home
        </Link>
        <Link
          className={isActive('/dashboard') ? 'active' : ''}
          to="/dashboard"
        >
          Dashboard
        </Link>
        <Link
          className={isActive('/comparison') ? 'active' : ''}
          to="/comparison"
        >
          Comparison
        </Link>
        <Link
          className={isActive('/about') ? 'active' : ''}
          to="/about"
        >
          About Us
        </Link>
        <Link
          className={isActive('/contact') ? 'active' : ''}
          to="/contact"
        >
          Contact Us
        </Link>
      </div>

      <div className={location.pathname === '/comparison' ? 'comparison-location-bar' : 'search-container'}>
        <button
          onClick={handleHomeClick}
          className="home-icon-btn"
          title="Replace the location"
        >
          <img src="/home.png" alt="Home" className="home-icon" />
        </button>

        <select
          value={selectedLocation}
          onChange={handleLocationChange}
          className="location-dropdown"
          title="Only change the location when changing the air quality condition measurement location"
        >
          <option value="" disabled>Select your location</option>
          <option value="deans-office">Dean's Office</option>
          <option value="basement">Basement</option>
          <option value="lecture-hall">Lecture Hall</option>
        </select>
      </div>
    </nav>
  );
};

export default Navbar;
