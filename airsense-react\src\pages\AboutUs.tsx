import React from 'react';
import Layout from '../layouts/Layout';

const AboutUs: React.FC = () => {
  return (
    <Layout showNavbar={true}>
      <div className="about-us-container">
        <header className="about-hero">
          <div className="container">
            <h1>About AirSense</h1>
            <p className="lead">Smart Indoor Air Quality Monitoring for a Healthier Tomorrow</p>
          </div>
        </header>

        <main className="container my-5">
          <div className="row">
            <div className="col-lg-8 mx-auto">
              <section className="mb-5">
                <h2>Our Mission</h2>
                <p>
                  At AirSense, we are dedicated to creating healthier indoor environments through 
                  advanced air quality monitoring technology. Our mission is to provide real-time, 
                  accurate air quality data that empowers individuals and organizations to make 
                  informed decisions about their indoor air quality.
                </p>
              </section>

              <section className="mb-5">
                <h2>What We Do</h2>
                <p>
                  AirSense offers a comprehensive indoor air quality monitoring system that tracks 
                  multiple environmental parameters including:
                </p>
                <ul>
                  <li>Air Quality Index (AQI) measurements</li>
                  <li>Temperature and humidity monitoring</li>
                  <li>Real-time data visualization</li>
                  <li>Historical data analysis and comparison</li>
                  <li>Location-based monitoring across different areas</li>
                </ul>
              </section>

              <section className="mb-5">
                <h2>Why Air Quality Matters</h2>
                <p>
                  Indoor air quality significantly impacts our health, productivity, and overall 
                  well-being. Poor air quality can lead to various health issues including 
                  respiratory problems, allergies, and reduced cognitive function. Our system 
                  helps you:
                </p>
                <ul>
                  <li>Identify potential air quality issues before they become problems</li>
                  <li>Monitor trends and patterns in your indoor environment</li>
                  <li>Make data-driven decisions about ventilation and air purification</li>
                  <li>Create healthier spaces for work, study, and living</li>
                </ul>
              </section>

              <section className="mb-5">
                <h2>Our Technology</h2>
                <p>
                  Built with cutting-edge sensor technology and modern web frameworks, AirSense 
                  provides accurate, real-time monitoring with an intuitive user interface. Our 
                  system features:
                </p>
                <ul>
                  <li>High-precision environmental sensors</li>
                  <li>Real-time data processing and visualization</li>
                  <li>Multi-location monitoring capabilities</li>
                  <li>Historical data storage and analysis</li>
                  <li>User-friendly dashboard and comparison tools</li>
                </ul>
              </section>

              <section className="mb-5">
                <h2>Our Commitment</h2>
                <p>
                  We are committed to continuous improvement and innovation in air quality 
                  monitoring technology. Our team works tirelessly to ensure that AirSense 
                  remains at the forefront of indoor environmental monitoring, providing you 
                  with the most accurate and actionable data possible.
                </p>
                <p>
                  Together, we can create healthier indoor environments for everyone.
                </p>
              </section>
            </div>
          </div>
        </main>
      </div>
    </Layout>
  );
};

export default AboutUs;
