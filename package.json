{"name": "air-sense", "version": "1.0.0", "description": "AirSense is a comprehensive indoor air quality monitoring system that provides real-time tracking, analysis, and comparison of air quality metrics across different locations. This repository contains both the original HTML/CSS/JS implementation and a modern React conversion.", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/AmandiArangala/Air-Sense.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/AmandiArangala/Air-Sense/issues"}, "homepage": "https://github.com/AmandiArangala/Air-Sense#readme"}