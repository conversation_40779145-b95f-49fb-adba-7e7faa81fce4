:root {
    --primary-color: #4FB8A3;
    --secondary-color: #3A8A98;
    --accent-color: #2E5C6E;
    --background-color: #f8f9fe;
    --card-background: #FFFFFF;
    --text-primary: #2C3E50;
    --text-secondary: #5F6368;
    --success-color: #34A853;
    --warning-color: #FBBC05;
    --danger-color:#EA4335; 
    --border-radius: 12px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0; 
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
}

html,body {
    background-color: var(--background-color);
    color: var(--text-primary);
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

.quality-scale-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    max-width: 1400px;
    margin: 2rem auto;
}

.quality-scale-card h2 {
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    color: var(--text-primary);
}

.scale-bar {
    height: 8px;
    background: linear-gradient(to right, 
        #34A853,  /* Good - Green */
        #FBBC05,  /* Moderate - Yellow */
        #EA4335   /* Poor - Red */
    );
    border-radius: 4px;
    margin: 1rem 0;
}

.scale-labels {
    display: flex;
    justify-content: space-between;
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.9rem;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2.5rem;
    width: 100%;
    margin: 2rem 0;
    padding: 0 1rem;
    justify-content: center;
    align-items: start;
}

.metric-card {
    background: var(--card-background);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    text-align: center;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    min-height: 380px;
    width: 100%;
    max-width: 280px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    margin: 0 auto;
    overflow: hidden;
    position: relative;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.metric-card h3 {
    font-size: 1.1rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
    font-weight: 600;
}

.gauge-container {
    position: relative;
    width: 180px;
    height: 180px;
    margin: 1rem auto;
}

.value-display {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 1;
}

.value-display .value {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--text-primary);
    line-height: 1;
}

.value-display .unit {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-top: 0.2rem;
}

.status {
    display: inline-block;
    padding: 0.5rem 1.4rem;
    border-radius: 20px;
    font-size: 1rem;
    font-weight: 700;
    margin-top: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 2px solid transparent;
}

.status.good {
    background: #34A853;
    color: white;
    box-shadow: 0 3px 12px rgba(52, 168, 83, 0.4);
    border: 2px solid #2d8f47;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.status.moderate {
    background: #FBBC05;
    color: #1a1a1a;
    box-shadow: 0 3px 12px rgba(251, 188, 5, 0.4);
    border: 2px solid #e6a800;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
}

.status.poor {
    background: #EA4335;
    color: white;
    box-shadow: 0 3px 12px rgba(234, 67, 53, 0.4);
    border: 2px solid #d33b2c;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}



.value-display {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.value-display .value {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--text-primary);
    line-height: 1;
}

.value-display .unit {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
}

.status-badge {
    margin-top: 1rem;
}

.badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: capitalize;
}

.badge-good {
    background-color: var(--success-color);
    color: white;
}

.badge-moderate {
    background-color: var(--warning-color);
    color: white;
}

.badge-poor {
    background-color: var(--danger-color);
    color: white;
}

.trends-section {
    background: var(--card-background);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--box-shadow);
    max-width: 1400px;
    margin: 2rem auto;
}

/* Current Location Alert */
.alert {
    max-width: 1400px;
    margin: 1rem auto;
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    border: none;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

/* Real-time Monitoring Card */
.card {
    background: var(--card-background);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    max-width: 1400px;
    margin: 1rem auto;
    border: none;
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    color: var(--text-primary);
    font-weight: 600;
}

/* Current Location Alert */
.alert {
    max-width: 1400px;
    margin: 1rem auto;
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

/* Real-time Monitoring Card */
.card {
    background: var(--card-background);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    max-width: 1400px;
    margin: 1rem auto;
    border: none;
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    color: var(--text-primary);
    font-weight: 600;
}

.trends-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.trends-header h2 {
    font-size: 1.5rem;
    color: var(--text-primary);
    margin: 0;
}

.trends-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.control-input {
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
    background: white;
}

.btn {
    padding: 0.5rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--secondary-color);
}

.chart-container {
    position: relative;
    height: 450px;
    width: 100%;
    margin-top: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
}

/* Responsive Design */
@media (min-width: 1400px) {
    .metrics-grid {
        grid-template-columns: repeat(5, 1fr);
        gap: 2.5rem;
    }
}

@media (min-width: 1200px) and (max-width: 1399px) {
    .metrics-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 2rem;
    }
}

@media (min-width: 992px) and (max-width: 1199px) {
    .metrics-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }

    .metric-card {
        max-width: 250px;
        min-height: 350px;
    }

    .gauge-container {
        width: 160px;
        height: 160px;
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .metric-card {
        max-width: 280px;
        min-height: 330px;
    }

    .gauge-container {
        width: 150px;
        height: 150px;
    }
}

@media (max-width: 767px) {
    .metrics-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        max-width: 350px;
        margin: 0 auto;
    }

    .metric-card {
        max-width: 100%;
        min-height: 320px;
    }

    .gauge-container {
        width: 140px;
        height: 140px;
    }

    .trends-header {
        flex-direction: column;
        align-items: stretch;
    }

    .trends-controls {
        justify-content: center;
    }

    .quality-scale-card,
    .trends-section,
    .alert,
    .card {
        margin: 1rem;
        padding: 1rem;
    }
}

@media (max-width: 480px) {
    .metric-card {
        min-height: 300px;
        padding: 1rem;
    }

    .gauge-container {
        width: 120px;
        height: 120px;
        min-width: 120px;
        min-height: 120px;
    }

    .metric-card h3 {
        font-size: 1rem;
    }

    .welcome-section h1 {
        font-size: 2rem;
    }

    .welcome-section .subtitle {
        font-size: 1rem;
    }
}

.dashboard-container {
    width: 100%;
    max-width: 1600px;
    margin: 0 auto;
    padding: 2rem;
    text-align: center;
}

.welcome-section {
    text-align: center;
    padding: 2rem 1rem;
    background: var(--card-background);
    margin-bottom: 2rem;
}

.welcome-section h1 {
    font-size: 2.5rem;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.welcome-section .subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
}
